{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-03T21:56:07.885Z", "updatedAt": "2025-08-03T21:56:07.888Z", "resourceCount": 4}, "resources": [{"id": "python-architect", "source": "project", "protocol": "execution", "name": "Python Architect 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/python-architect/execution/python-architect.execution.md", "metadata": {"createdAt": "2025-08-03T21:56:07.887Z", "updatedAt": "2025-08-03T21:56:07.887Z", "scannedAt": "2025-08-03T21:56:07.887Z", "path": "domain/python-architect/execution/python-architect.execution.md"}}, {"id": "python-architect", "source": "project", "protocol": "knowledge", "name": "Python Architect 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/python-architect/knowledge/python-architect.knowledge.md", "metadata": {"createdAt": "2025-08-03T21:56:07.887Z", "updatedAt": "2025-08-03T21:56:07.887Z", "scannedAt": "2025-08-03T21:56:07.887Z", "path": "domain/python-architect/knowledge/python-architect.knowledge.md"}}, {"id": "python-architect", "source": "project", "protocol": "role", "name": "Python Architect 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/python-architect/python-architect.role.md", "metadata": {"createdAt": "2025-08-03T21:56:07.888Z", "updatedAt": "2025-08-03T21:56:07.888Z", "scannedAt": "2025-08-03T21:56:07.888Z", "path": "domain/python-architect/python-architect.role.md"}}, {"id": "python-architect", "source": "project", "protocol": "thought", "name": "Python Architect 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/python-architect/thought/python-architect.thought.md", "metadata": {"createdAt": "2025-08-03T21:56:07.888Z", "updatedAt": "2025-08-03T21:56:07.888Z", "scannedAt": "2025-08-03T21:56:07.888Z", "path": "domain/python-architect/thought/python-architect.thought.md"}}], "stats": {"totalResources": 4, "byProtocol": {"execution": 1, "knowledge": 1, "role": 1, "thought": 1}, "bySource": {"project": 4}}}