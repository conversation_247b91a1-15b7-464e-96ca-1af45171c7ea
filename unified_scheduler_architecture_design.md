# 统一MiniBootScheduler架构设计方案

## 1. 架构设计目标

### 1.1 核心目标
- **消除循环依赖**：MiniBootScheduler不再依赖外部TaskManager
- **统一接口**：提供单一、一致的任务管理API
- **功能完整性**：保持所有现有功能（监控、重试、固定延迟处理）
- **向后兼容**：确保现有代码无需修改即可使用
- **性能优化**：减少对象创建和方法调用层次

### 1.2 设计原则
- **单一职责**：MiniBootScheduler负责所有调度相关功能
- **组合优于继承**：使用内部组件而非继承关系
- **接口简化**：统一的add_task方法替代多个重复接口
- **线程安全**：保持现有的线程安全机制

## 2. 新架构类结构设计

### 2.1 MiniBootScheduler核心结构
```python
class MiniBootScheduler:
    """统一的任务调度器 - 集成所有调度功能"""

    def __init__(self, ...):
        # === 现有属性保持不变 ===
        self.properties = properties
        self.state = SchedulerState.STOPPED
        self.task_registry = TaskRegistry()
        self._scheduler = self._create_scheduler()
        self._execution_stats = {...}
        self._lock = threading.RLock()

        # === 新增：集成的任务管理功能 ===
        # 任务包装器存储（原TaskManager功能）
        self.task_wrappers: dict[str, TaskWrapper] = {}

        # 固定延迟任务处理器（原TaskManager功能）
        self.fixed_delay_handler = FixedDelayTaskHandler(self)

        # 移除：不再创建独立的TaskManager
        # self.task_manager = TaskManager(self)  # 删除此行
```

### 2.2 内部组件设计

#### 2.2.1 TaskExecutionMetrics（内部类）
```python
class TaskExecutionMetrics:
    """任务执行指标（作为MiniBootScheduler的内部组件）"""
    # 保持现有所有功能不变
    # 线程安全机制保持不变
```

#### 2.2.2 TaskWrapper（内部类）
```python
class TaskWrapper:
    """任务包装器（作为MiniBootScheduler的内部组件）"""

    def __init__(self, task: ScheduledTask, scheduler: MiniBootScheduler):
        # 直接引用MiniBootScheduler，消除循环依赖
        self.task = task
        self.scheduler = scheduler  # 不再是scheduler_ref
        self.metrics = TaskExecutionMetrics()
        # ... 其他属性保持不变
```

#### 2.2.3 FixedDelayTaskHandler（内部类）
```python
class FixedDelayTaskHandler:
    """固定延迟任务处理器（作为MiniBootScheduler的内部组件）"""

    def __init__(self, scheduler: MiniBootScheduler):
        # 直接引用MiniBootScheduler
        self.scheduler = scheduler  # 不再是scheduler_ref
        # ... 其他属性保持不变
```

## 3. 统一任务管理接口设计

### 3.1 核心接口：add_task
```python
def add_task(self,
             task: ScheduledTask,
             max_retries: int = 3,
             retry_delay: float = 1.0,
             enable_monitoring: bool = True) -> str:
    """
    统一的任务添加接口

    Args:
        task: 要添加的任务
        max_retries: 最大重试次数
        retry_delay: 重试延迟(秒)
        enable_monitoring: 是否启用监控功能

    Returns:
        任务ID
    """
    # 集成原TaskManager.add_task的所有逻辑
    # 支持所有任务类型：CRON, FIXED_RATE, FIXED_DELAY, ONE_TIME
```

### 3.2 便捷创建方法
```python
def create_method_task(self, method, instance=None, config=None, **kwargs) -> str:
    """创建方法任务（集成原TaskManager.create_method）"""

def create_lambda_task(self, func, config=None, **kwargs) -> str:
    """创建Lambda任务（集成原TaskManager.create_lambda）"""

def from_method_task(self, method, instance=None, **kwargs) -> Optional[str]:
    """从@Scheduled装饰的方法创建任务"""
```

### 3.3 任务管理方法
```python
def remove_task(self, task_id: str) -> bool:
    """移除任务（集成原TaskManager.remove_task）"""

def get_task_metrics(self, task_id: str) -> Optional[dict]:
    """获取任务指标（集成原TaskManager.metrics）"""

def get_all_metrics(self) -> dict:
    """获取所有任务指标（集成原TaskManager.all_metrics）"""

def update_task_config(self, task_id: str, **kwargs) -> bool:
    """更新任务配置"""
```

## 4. 接口统一策略

### 4.1 移除重复接口
```python
# 删除这些重复的方法：
# - add_managed_task  → 统一为 add_task
# - managed_method    → 统一为 create_method_task
# - managed_lambda    → 统一为 create_lambda_task
# - metrics           → 统一为 get_task_metrics
# - all_metrics       → 统一为 get_all_metrics
```

### 4.2 保留兼容性方法
```python
# 为向后兼容保留别名方法
def schedule_task(self, task: ScheduledTask) -> str:
    """兼容方法：调用add_task，不启用高级功能"""
    return self.add_task(task, max_retries=0, enable_monitoring=False)

def unschedule_task(self, task_id: str) -> bool:
    """兼容方法：调用remove_task"""
    return self.remove_task(task_id)
```

## 5. 线程安全机制

### 5.1 锁策略
```python
class MiniBootScheduler:
    def __init__(self, ...):
        # 使用单一的RLock管理所有状态
        self._lock = threading.RLock()

        # 各组件使用相同的锁或独立的锁
        # TaskWrapper内部保持独立的锁
        # FixedDelayTaskHandler使用独立的锁
```

### 5.2 线程安全保证
- **状态管理**：所有状态变更都在锁保护下进行
- **任务注册**：task_wrappers字典的访问都加锁
- **指标收集**：TaskExecutionMetrics内部保持独立锁
- **固定延迟处理**：FixedDelayTaskHandler保持独立锁

## 6. 向后兼容性方案

### 6.1 API别名支持
```python
# 在__init__.py中提供别名
from .scheduler import MiniBootScheduler

# 向后兼容别名
TaskManager = MiniBootScheduler
```

### 6.2 方法别名
```python
class MiniBootScheduler:
    # 保留原有方法名作为别名
    def add_managed_task(self, task, max_retries=3, retry_delay=1.0):
        """向后兼容方法"""
        return self.add_task(task, max_retries, retry_delay)

    def managed_method(self, method, **kwargs):
        """向后兼容方法"""
        return self.create_method_task(method, **kwargs)
```

## 7. 性能优化设计

### 7.1 减少对象创建
- 消除TaskManager中间层
- 直接在MiniBootScheduler中管理TaskWrapper
- 减少方法调用链路

### 7.2 内存优化
- 统一的锁管理减少锁竞争
- 内部组件共享调度器状态
- 避免重复的状态存储

## 8. 实施优先级

### 8.1 高优先级（核心功能）
1. TaskExecutionMetrics集成
2. TaskWrapper集成
3. 统一add_task接口
4. 基础任务管理功能

### 8.2 中优先级（高级功能）
1. FixedDelayTaskHandler集成
2. 便捷创建方法
3. 指标查询功能

### 8.3 低优先级（兼容性）
1. 向后兼容别名
2. 方法重命名
3. 文档更新

## 9. 架构优势分析

### 9.1 解决的问题
- **循环依赖消除**：MiniBootScheduler自包含，无外部依赖
- **接口简化**：单一add_task方法替代多个重复接口
- **性能提升**：减少对象创建和方法调用层次
- **维护性提高**：代码集中，职责清晰

### 9.2 保持的优势
- **功能完整性**：所有现有功能完全保留
- **线程安全**：现有的线程安全机制完全保持
- **扩展性**：内部组件化设计便于功能扩展
- **兼容性**：向后兼容，现有代码无需修改

## 10. 风险控制

### 10.1 技术风险
- **迁移复杂性**：通过分步骤迁移降低风险
- **功能遗漏**：详细的功能清单确保完整迁移
- **性能回退**：保持现有的优化机制

### 10.2 兼容性风险
- **API变更**：通过别名方法保持兼容
- **行为变更**：保持所有方法的原有行为
- **测试覆盖**：确保所有功能测试通过
