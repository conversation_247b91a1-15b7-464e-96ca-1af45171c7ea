#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 异步组件基类 - 提供通用的初始化检查和生命周期管理
"""

from abc import ABC, abstractmethod
from concurrent.futures import Future
from typing import Any, Callable, Union




class Executor(ABC):
    """异步执行器抽象接口

    参考 Spring Boot 的 TaskExecutor 接口设计，提供统一的异步执行抽象。
    """

    @abstractmethod
    def submit(self, func: Callable[..., Any], *args: Any, **kwargs: Any) -> Union[Future[Any], Any]:
        """提交任务执行

        Args:
            func: 要执行的函数
            *args: 函数参数
            **kwargs: 函数关键字参数

        Returns:
            Union[Future, Any]: 通常返回 Future 对象，但某些拒绝策略下可能直接返回执行结果
        """
        pass

    @abstractmethod
    def shutdown(self, wait: bool = True) -> None:
        """关闭执行器

        Args:
            wait: 是否等待任务完成
        """
        pass

    @abstractmethod
    def is_shutdown(self) -> bool:
        """检查是否已关闭

        Returns:
            bool: 是否已关闭
        """
        pass

    @abstractmethod
    def get_metrics(self) -> dict[str, Any]:
        """获取执行器指标

        Returns:
            dict: 包含执行器各项指标的字典
        """
        pass
