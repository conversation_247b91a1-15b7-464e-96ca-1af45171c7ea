#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Executor 基类单元测试

测试异步执行器抽象基类的接口定义.
"""

import unittest
from abc import ABC
from concurrent.futures import Future
from typing import Any, Callable

from miniboot.asyncs.base import Executor


class TestExecutorInterface(unittest.TestCase):
    """Executor 接口测试"""

    def test_executor_is_abstract(self):
        """测试 Executor 是抽象类"""
        self.assertTrue(issubclass(Executor, ABC))

        # 不能直接实例化抽象类
        with self.assertRaises(TypeError):
            Executor()  # type: ignore[abstract]

    def test_executor_abstract_methods(self):
        """测试 Executor 的抽象方法"""
        # 检查抽象方法是否存在
        abstract_methods = Executor.__abstractmethods__

        expected_methods = {"submit", "shutdown", "is_shutdown", "get_metrics"}
        self.assertEqual(abstract_methods, expected_methods)

    def test_concrete_implementation_required_methods(self):
        """测试具体实现必须实现所有抽象方法"""

        # 不完整的实现（缺少方法）
        class IncompleteExecutor(Executor):
            def submit(self, func: Callable[..., Any], *args: Any, **kwargs: Any) -> Future[Any]:  # noqa: ARG002
                """提交任务执行 - 不完整实现用于测试抽象类"""
                from concurrent.futures import Future

                future: Future[Any] = Future()
                # 这是一个不完整的实现，用于测试抽象类无法实例化
                # 实际上不会执行到这里，因为缺少其他抽象方法
                return future

            # 缺少其他方法

        # 不能实例化不完整的实现
        with self.assertRaises(TypeError):
            IncompleteExecutor() # type: ignore[abstract]

    def test_complete_implementation(self):
        """测试完整的实现"""

        class MockExecutor(Executor):
            def __init__(self):
                self._shutdown = False
                self._metrics = {"tasks": 0}

            def submit(self, func: Callable[..., Any], *args: Any, **kwargs: Any) -> Future[Any]:
                from concurrent.futures import Future

                future: Future[Any] = Future()
                future.set_result(func(*args, **kwargs))
                self._metrics["tasks"] += 1
                return future

            def shutdown(self, wait: bool = True) -> None:  # noqa: ARG002
                self._shutdown = True

            def is_shutdown(self) -> bool:
                return self._shutdown

            def get_metrics(self) -> dict[str, Any]:
                return self._metrics.copy()

        # 应该能够成功实例化
        executor = MockExecutor()
        self.assertIsInstance(executor, Executor)
        self.assertFalse(executor.is_shutdown())

    def test_method_signatures(self):
        """测试方法签名"""

        class TestExecutor(Executor):
            def submit(self, func: Callable[..., Any], *args: Any, **kwargs: Any) -> Future[Any]:  # noqa: ARG002
                from concurrent.futures import Future

                return Future()

            def shutdown(self, wait: bool = True) -> None:  # noqa: ARG002
                pass

            def is_shutdown(self) -> bool:
                return False

            def get_metrics(self) -> dict[str, Any]:
                return {}

        executor = TestExecutor()

        # 测试 submit 方法
        def dummy_func():
            return "test"

        future = executor.submit(dummy_func)
        self.assertIsInstance(future, Future)

        # 测试 shutdown 方法
        executor.shutdown()  # 默认参数
        executor.shutdown(wait=True)  # 显式参数
        executor.shutdown(wait=False)  # 显式参数

        # 测试 is_shutdown 方法
        result = executor.is_shutdown()
        self.assertIsInstance(result, bool)

        # 测试 get_metrics 方法
        metrics = executor.get_metrics()
        self.assertIsInstance(metrics, dict)


class TestExecutorBehavior(unittest.TestCase):
    """Executor 行为测试"""

    def create_mock_executor(self):
        """创建模拟执行器"""

        class MockExecutor(Executor):
            def __init__(self):
                self._shutdown = False
                self._tasks = []
                self._metrics = {"total_tasks": 0, "completed_tasks": 0, "failed_tasks": 0}

            def submit(self, func: Callable[..., Any], *args: Any, **kwargs: Any) -> Future[Any]:
                from concurrent.futures import Future

                if self._shutdown:
                    raise RuntimeError("Executor is shutdown")

                future: Future[Any] = Future()
                self._tasks.append(future)
                self._metrics["total_tasks"] += 1

                try:
                    result = func(*args, **kwargs)
                    future.set_result(result)
                    self._metrics["completed_tasks"] += 1
                except Exception as e:
                    future.set_exception(e)
                    self._metrics["failed_tasks"] += 1

                return future

            def shutdown(self, wait: bool = True) -> None:
                self._shutdown = True
                if wait:
                    # 等待所有任务完成
                    for future in self._tasks:
                        if not future.done():
                            future.cancel()

            def is_shutdown(self) -> bool:
                return self._shutdown

            def get_metrics(self) -> dict[str, Any]:
                return self._metrics.copy()

        return MockExecutor()

    def test_submit_and_execute(self):
        """测试任务提交和执行"""
        executor = self.create_mock_executor()

        def add_numbers(a, b):
            return a + b

        # 提交任务
        future = executor.submit(add_numbers, 3, 5)

        # 验证结果
        self.assertEqual(future.result(), 8)

        # 验证指标
        metrics = executor.get_metrics()
        self.assertEqual(metrics["total_tasks"], 1)
        self.assertEqual(metrics["completed_tasks"], 1)
        self.assertEqual(metrics["failed_tasks"], 0)

    def test_submit_with_exception(self):
        """测试任务执行异常"""
        executor = self.create_mock_executor()

        def failing_task():
            raise ValueError("Test error")

        # 提交会失败的任务
        future = executor.submit(failing_task)

        # 验证异常
        with self.assertRaises(ValueError) as cm:
            future.result()
        self.assertIn("Test error", str(cm.exception))

        # 验证指标
        metrics = executor.get_metrics()
        self.assertEqual(metrics["total_tasks"], 1)
        self.assertEqual(metrics["completed_tasks"], 0)
        self.assertEqual(metrics["failed_tasks"], 1)

    def test_submit_with_args_and_kwargs(self):
        """测试带参数的任务提交"""
        executor = self.create_mock_executor()

        def complex_task(a, b, c=None, d=None):
            return f"a={a}, b={b}, c={c}, d={d}"

        # 提交带位置参数和关键字参数的任务
        future = executor.submit(complex_task, 1, 2, c=3, d=4)

        # 验证结果
        result = future.result()
        self.assertEqual(result, "a=1, b=2, c=3, d=4")

    def test_shutdown_behavior(self):
        """测试关闭行为"""
        executor = self.create_mock_executor()

        # 初始状态
        self.assertFalse(executor.is_shutdown())

        # 关闭执行器
        executor.shutdown()

        # 验证状态
        self.assertTrue(executor.is_shutdown())

        # 关闭后不能提交新任务
        with self.assertRaises(RuntimeError) as cm:
            executor.submit(lambda: None)
        self.assertIn("Executor is shutdown", str(cm.exception))

    def test_shutdown_with_wait_parameter(self):
        """测试带等待参数的关闭"""
        executor = self.create_mock_executor()

        # 测试 wait=True
        executor.shutdown(wait=True)
        self.assertTrue(executor.is_shutdown())

        # 重新创建执行器测试 wait=False
        executor = self.create_mock_executor()
        executor.shutdown(wait=False)
        self.assertTrue(executor.is_shutdown())

    def test_metrics_tracking(self):
        """测试指标跟踪"""
        executor = self.create_mock_executor()

        # 初始指标
        metrics = executor.get_metrics()
        self.assertEqual(metrics["total_tasks"], 0)
        self.assertEqual(metrics["completed_tasks"], 0)
        self.assertEqual(metrics["failed_tasks"], 0)

        # 执行成功任务
        future1 = executor.submit(lambda: "success")
        future1.result()

        # 执行失败任务
        future2 = executor.submit(lambda: 1 / 0)  # ZeroDivisionError
        with self.assertRaises(ZeroDivisionError):
            future2.result()

        # 检查更新后的指标
        metrics = executor.get_metrics()
        self.assertEqual(metrics["total_tasks"], 2)
        self.assertEqual(metrics["completed_tasks"], 1)
        self.assertEqual(metrics["failed_tasks"], 1)

    def test_metrics_immutability(self):
        """测试指标的不可变性"""
        executor = self.create_mock_executor()

        # 获取指标
        metrics1 = executor.get_metrics()
        metrics2 = executor.get_metrics()

        # 修改返回的指标不应影响内部状态
        metrics1["total_tasks"] = 999

        # 重新获取指标应该不受影响
        metrics3 = executor.get_metrics()
        self.assertEqual(metrics3["total_tasks"], 0)
        self.assertEqual(metrics2["total_tasks"], 0)

    def test_multiple_task_execution(self):
        """测试多任务执行"""
        executor = self.create_mock_executor()

        # 提交多个任务
        futures = []
        for i in range(5):
            future = executor.submit(lambda x: x * 2, i)
            futures.append(future)

        # 验证所有任务结果
        results = [future.result() for future in futures]
        expected = [i * 2 for i in range(5)]
        self.assertEqual(results, expected)

        # 验证指标
        metrics = executor.get_metrics()
        self.assertEqual(metrics["total_tasks"], 5)
        self.assertEqual(metrics["completed_tasks"], 5)
        self.assertEqual(metrics["failed_tasks"], 0)


if __name__ == '__main__':
    unittest.main()
